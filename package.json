{"name": "chrome-extension-v3-Bark", "version": "2.0.0", "description": "Push to iPhone using Bark - Chrome Extension with Vue 3.5 and Manifest V3", "type": "module", "packageManager": "pnpm@9.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "clean": "rimraf dist node_modules/.vite"}, "dependencies": {"vue": "^3.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "vite": "^5.4.0", "rimraf": "^6.0.0"}}