// 推送消息工具函数
export class PushService {
  constructor() {
    this.autoCopyFlag = "0";
  }

  // 发送消息
  async sendMessage(content, fullServerUrl = "") {
    const { storage } = await import('./storage.js');
    const settings = await storage.get({
      server_urls: [],
      auto_copy: "no"
    });

    if (!settings.server_urls?.length) {
      this.showError("请在选项中设置服务器URL！");
      return;
    }

    this.autoCopyFlag = settings.auto_copy === "yes" ? "1" : "0";

    try {
      fullServerUrl = fullServerUrl || settings.server_urls[0].server_url;
      fullServerUrl = fullServerUrl.replace(/^selection#/, '');

      const { origin, pathname, search } = new URL(fullServerUrl);
      const params = search.slice(1);
      const baseUrl = `${origin}${pathname}/`;
      const pushUrl = `${baseUrl}${encodeURIComponent(content)}?automaticallyCopy=${this.autoCopyFlag}${params ? '&' + params : ''}`;

      const isHttpUrl = /^https?:\/\//i.test(fullServerUrl);

      if (isHttpUrl) {
        await this.httpRequest(pushUrl, content);
      } else {
        throw new Error('仅支持 HTTP/HTTPS URL 格式的 Bark 推送地址');
      }
    } catch (error) {
      console.error('Error processing URL:', error);
      this.showError("URL处理错误");
    }
  }

  // HTTP 请求
  async httpRequest(url, content) {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.text();
      this.showNotification(content, result !== 'error');
    } catch (error) {
      console.error('Request failed:', error);
      this.showNotification(content, false);
    }
  }



  // 显示通知
  showNotification(content, success) {
    const notificationOptions = {
      icon: "assets/bark_128.png",
      body: success ? content : "发送消息失败"
    };
    
    new Notification(
      success ? "消息已发送" : "发送失败", 
      notificationOptions
    );
  }

  // 显示错误
  showError(message) {
    // 在 service worker 中不能使用 alert，改用通知
    this.showNotification(message, false);
    chrome.tabs.create({ url: "options.html" });
  }

  // 验证 URL
  static validateURL(str) {
    const regex = /(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/;
    return regex.test(str);
  }
}
