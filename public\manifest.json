{"manifest_version": 3, "name": "Bark Notify", "version": "2.0.0", "description": "Push to iPhone using Bark", "permissions": ["activeTab", "storage", "notifications", "contextMenus", "clipboardRead"], "host_permissions": ["*://*/*"], "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["*://*/*"], "js": ["content.js"]}], "web_accessible_resources": [{"resources": ["assets/*"], "matches": ["*://*/*"]}], "icons": {"16": "assets/bark_16.png", "48": "assets/bark_48.png", "128": "assets/bark_128.png"}, "options_page": "options.html", "action": {"default_popup": "popup.html", "default_icon": {"16": "assets/bark_16.png", "48": "assets/bark_48.png", "128": "assets/bark_128.png"}, "default_title": "Bark-revision"}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; style-src 'self' 'unsafe-inline';"}}