import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import fs from 'fs'

export default defineConfig({
  base: './',
  css: {
    postcss: './postcss.config.js'
  },
  plugins: [
    vue(),
    // 自定义插件来处理 Chrome Extension 的特殊需求
    {
      name: 'chrome-extension',
      generateBundle() {
        // 复制 manifest.json
        this.emitFile({
          type: 'asset',
          fileName: 'manifest.json',
          source: fs.readFileSync('public/manifest.json', 'utf-8')
        })
      },
      writeBundle() {
        // 构建完成后移动 HTML 文件到根目录并修复路径
        try {
          // 处理 popup.html
          if (fs.existsSync('dist/src/popup/index.html')) {
            let content = fs.readFileSync('dist/src/popup/index.html', 'utf-8')
            // 修复所有资源路径
            content = content.replace(/src="\/assets\//g, 'src="assets/')
            content = content.replace(/href="\/assets\//g, 'href="assets/')
            content = content.replace(/src="\.\.\/\.\.\/assets\//g, 'src="assets/')
            content = content.replace(/href="\.\.\/\.\.\/assets\//g, 'href="assets/')
            // 确保相对路径正确
            content = content.replace(/assets\//g, 'assets/')
            fs.writeFileSync('dist/popup.html', content)
          }

          // 处理 options.html
          if (fs.existsSync('dist/src/options/index.html')) {
            let content = fs.readFileSync('dist/src/options/index.html', 'utf-8')
            // 修复所有资源路径
            content = content.replace(/src="\/assets\//g, 'src="assets/')
            content = content.replace(/href="\/assets\//g, 'href="assets/')
            content = content.replace(/src="\.\.\/\.\.\/assets\//g, 'src="assets/')
            content = content.replace(/href="\.\.\/\.\.\/assets\//g, 'href="assets/')
            // 确保相对路径正确
            content = content.replace(/assets\//g, 'assets/')
            fs.writeFileSync('dist/options.html', content)
          }

          // 清理空的 src 目录
          if (fs.existsSync('dist/src')) {
            fs.rmSync('dist/src', { recursive: true, force: true })
          }

          console.log('Chrome Extension build completed! HTML files moved to root with fixed paths.')
        } catch (error) {
          console.error('Error moving HTML files:', error)
        }
      }
    }
  ],
  build: {
    cssCodeSplit: false,
    rollupOptions: {
      input: {
        popup: resolve(__dirname, 'src/popup/index.html'),
        options: resolve(__dirname, 'src/options/index.html'),
        background: resolve(__dirname, 'src/background/background.js'),
        content: resolve(__dirname, 'src/content/content.js')
      },
      output: {
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'background' || chunkInfo.name === 'content') {
            return `${chunkInfo.name}.js`
          }
          return `assets/[name]-[hash].js`
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.names?.[0] || assetInfo.name || ''
          // 处理图标文件
          if (info.endsWith('.png')) {
            return 'assets/[name].[ext]'
          }
          return 'assets/[name]-[hash].[ext]'
        }
      },
      external: ['chrome']
    },
    outDir: 'dist',
    emptyOutDir: true,
    target: 'es2017'
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  publicDir: 'public'
})
