// Background Script for Manifest V3 (Service Worker)
import { storage, defaultSettings } from '../utils/storage.js';
import { PushService } from '../utils/push.js';

const pushService = new PushService();

// 处理扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  try {
    const response = await chrome.tabs.sendMessage(tab.id, {
      method: "getSelection"
    });
    await handleGlobalPush(response, tab);
  } catch (error) {
    console.error('Error handling action click:', error);
    await handleGlobalPush(null, tab);
  }
});

// 全局推送处理
async function handleGlobalPush(response, tab) {
  const settings = await storage.get(defaultSettings);

  // 如果默认推送内容是 URL，推送 URL
  if (settings.default_push_content === "URL") {
    if (response?.data && response.data !== '') {
      console.log("send selected text: " + response.data);
      await pushService.sendMessage(response.data);
    } else {
      console.log("send url", tab);
      await sendCurrentUrl();
    }
  } else if (settings.default_push_content === "clipboard") {
    // 如果默认是剪贴板，推送剪贴板数据
    await sendClipboardData();
  }
}

// 发送当前页面 URL
async function sendCurrentUrl() {
  try {
    const tabs = await chrome.tabs.query({
      active: true,
      lastFocusedWindow: true
    });

    if (tabs.length > 0) {
      const currentUrl = tabs[0].url;
      await pushService.sendMessage(currentUrl);
      console.log('Sent URL:', currentUrl);
    }
  } catch (error) {
    console.error('Error sending URL:', error);
  }
}

// 发送剪贴板数据
async function sendClipboardData() {
  try {
    const clipboardData = await getClipboardData();
    if (clipboardData) {
      await pushService.sendMessage(clipboardData);
    }
  } catch (error) {
    console.error('Error sending clipboard data:', error);
  }
}

// 获取剪贴板数据
async function getClipboardData() {
  try {
    const text = await navigator.clipboard.readText();
    console.log("clipboard content: " + text);
    return text;
  } catch (error) {
    console.error('Error reading clipboard:', error);
    return '';
  }
}

// 右键菜单处理
async function handleContextMenu(info, tab) {
  console.log("=== Context Menu Clicked ===");
  console.log("Menu ID:", info.menuItemId);
  console.log("Selection text:", info.selectionText);
  console.log("Media type:", info.mediaType);

  // 处理测试菜单
  if (info.menuItemId === "bark-test") {
    console.log("Test menu clicked!");
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'assets/bark_128.png',
      title: 'Bark 测试',
      message: '右键菜单功能正常！'
    });
    return;
  }

  // 处理错误菜单
  if (info.menuItemId === "bark-error") {
    chrome.tabs.create({ url: "options.html" });
    return;
  }

  // 如果点击的是 "no-devices" 菜单，打开设置页面
  if (info.menuItemId === "no-devices") {
    chrome.tabs.create({ url: "options.html" });
    return;
  }

  if (info.mediaType === "image") {
    await pushService.sendMessage(info.srcUrl, info.menuItemId, "image");
  } else {
    if (typeof info.selectionText === 'undefined') {
      await handleGlobalPush(null, tab);
    } else {
      await pushService.sendMessage(info.selectionText, info.menuItemId);
    }
  }
}

// 注册右键菜单
async function registerContextMenus() {
  try {
    console.log('=== Starting Context Menu Registration ===');

    // 清除所有现有菜单
    await chrome.contextMenus.removeAll();
    console.log('Cleared all existing context menus');

    // 先创建一个测试菜单，确保基本功能正常
    chrome.contextMenus.create({
      id: "bark-test",
      title: "Bark 测试菜单",
      contexts: ["all"]
    });
    console.log('Created test menu');

    // 获取设置
    const settings = await storage.get(null);
    console.log('Raw settings:', settings);
    console.log('server_urls:', settings.server_urls);
    console.log('server_urls type:', typeof settings.server_urls);
    console.log('server_urls is array:', Array.isArray(settings.server_urls));
    console.log('server_urls length:', settings.server_urls ? settings.server_urls.length : 'undefined');

    // 如果没有配置设备，创建一个提示菜单
    if (!settings.server_urls || settings.server_urls.length === 0) {
      console.log('No devices configured, creating placeholder menu');
      chrome.contextMenus.create({
        id: "no-devices",
        title: "Bark: 请先配置推送设备",
        contexts: ["all"]
      });
      console.log('Placeholder menu created');
      return;
    }

    console.log('Creating menus for', settings.server_urls.length, 'devices');
    // 为每个服务器 URL 创建菜单项
    settings.server_urls.forEach((server, index) => {
      console.log(`Creating menu for device ${index}:`, server);

      // 创建普通推送菜单
      chrome.contextMenus.create({
        id: server.server_url,
        title: `Bark: 推送到 ${server.server_name}`,
        contexts: ["all"]
      });
      console.log(`Created general menu for ${server.server_name}`);

      // 创建选择文本和图片的推送菜单
      chrome.contextMenus.create({
        id: `selection#${server.server_url}`,
        title: `Bark: 发送到 ${server.server_name}`,
        contexts: ["selection", "image"]
      });
      console.log(`Created selection menu for ${server.server_name}`);
    });

    console.log('=== Context menus registered successfully ===');
  } catch (error) {
    console.error('Error registering context menus:', error);
    console.error('Error stack:', error.stack);

    // 如果出错，至少创建一个基本菜单
    try {
      chrome.contextMenus.create({
        id: "bark-error",
        title: "Bark: 菜单注册出错",
        contexts: ["all"]
      });
    } catch (fallbackError) {
      console.error('Even fallback menu creation failed:', fallbackError);
    }
  }
}

// 监听右键菜单点击
chrome.contextMenus.onClicked.addListener(handleContextMenu);

// 监听来自其他脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log(sender.tab ?
    "from a content script:" + sender.tab.url :
    "from the extension");

  if (request.greeting === "hello") {
    registerContextMenus().then(() => {
      sendResponse({ farewell: "goodbye" });
    });
    return true; // 保持消息通道开放
  }

  sendResponse({ farewell: "goodbye" });
});

// 扩展启动时注册菜单
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension startup - registering context menus');
  registerContextMenus();
});

chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed - registering context menus');
  registerContextMenus();
});

// 立即注册菜单（确保扩展加载时就有菜单）
console.log('Background script loaded - immediately registering context menus');
registerContextMenus();