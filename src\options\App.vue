<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <!-- 头部 -->
      <div class="card mb-8">
        <div class="flex items-center mb-4">
          <img src="/assets/bark_48.png" alt="Bark" class="w-12 h-12 mr-4">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Bark Extension 设置</h1>
            <p class="text-gray-600 dark:text-gray-400">配置您的推送设备和偏好设置</p>
          </div>
        </div>
      </div>

      <!-- 状态消息 -->
      <div v-if="statusMessage" class="mb-6 p-4 rounded-lg" :class="statusClass">
        {{ statusMessage }}
      </div>

      <!-- 默认推送内容设置 -->
      <div class="card mb-8">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">默认推送内容</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">此选项控制点击工具栏图标时默认推送的内容。</p>
        
        <div class="space-y-3">
          <div class="radio-group">
            <input 
              type="radio" 
              id="clipboard" 
              v-model="settings.default_push_content" 
              value="clipboard"
              class="radio-input"
              @change="saveSettings"
            >
            <label for="clipboard" class="text-gray-700 dark:text-gray-300">剪贴板内容</label>
          </div>

          <div class="radio-group">
            <input
              type="radio"
              id="URL"
              v-model="settings.default_push_content"
              value="URL"
              class="radio-input"
              @change="saveSettings"
            >
            <label for="URL" class="text-gray-700 dark:text-gray-300">当前页面 URL</label>
          </div>
        </div>
      </div>

      <!-- 自动复制设置 -->
      <div class="card mb-8">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">自动复制</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">推送到设备后是否自动复制到剪贴板。</p>

        <div class="space-y-3">
          <div class="radio-group">
            <input
              type="radio"
              id="yes"
              v-model="settings.auto_copy"
              value="yes"
              class="radio-input"
              @change="saveSettings"
            >
            <label for="yes" class="text-gray-700 dark:text-gray-300">是</label>
          </div>

          <div class="radio-group">
            <input
              type="radio"
              id="no"
              v-model="settings.auto_copy"
              value="no"
              class="radio-input"
              @change="saveSettings"
            >
            <label for="no" class="text-gray-700 dark:text-gray-300">否</label>
          </div>
        </div>
      </div>

      <!-- 设备管理 -->
      <div class="card mb-8">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">设备管理</h2>

        <!-- 现有设备列表 -->
        <div v-if="Array.isArray(settings.server_urls) && settings.server_urls.length > 0" class="mb-6">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">已配置的设备</h3>
            <button
              @click="testAllDevices"
              :disabled="isTestingAll"
              class="btn-secondary text-sm"
            >
              <span v-if="!isTestingAll">测试全部设备</span>
              <span v-else>测试中...</span>
            </button>
          </div>
          <div class="space-y-3">
            <div
              v-for="(server, index) in settings.server_urls"
              :key="index"
              class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
            >
              <div class="flex-1">
                <div class="font-medium text-gray-900 dark:text-gray-100">{{ server.server_name }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400 break-all">{{ server.server_url }}</div>
                <!-- 测试状态显示 -->
                <div v-if="testResults[index]" class="mt-2 flex items-center text-sm" :class="testResults[index].success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  <svg v-if="testResults[index].success" class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  <svg v-else class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                  </svg>
                  <span>{{ testResults[index].message }}</span>
                </div>
              </div>
              <div class="flex items-center space-x-2 ml-4">
                <!-- 测试按钮 -->
                <button
                  @click="testDevice(index)"
                  :disabled="testingStates[index]"
                  class="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50"
                  title="测试推送"
                >
                  <svg v-if="!testingStates[index]" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  <svg v-else class="w-5 h-5 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/>
                  </svg>
                </button>
                <!-- 删除按钮 -->
                <button
                  @click="deleteServer(index)"
                  class="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors"
                  title="删除设备"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 添加新设备 -->
        <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
          <h3 class="text-lg font-medium mb-4 text-gray-900 dark:text-gray-100">添加新设备</h3>



          <!-- 设备信息输入 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label for="server_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">设备别名</label>
              <input
                type="text"
                id="server_name"
                v-model="newDevice.name"
                placeholder="例如：我的iPhone"
                class="input-field"
              >
            </div>

            <div>
              <label for="server_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">推送地址</label>
              <input
                type="text"
                id="server_url"
                v-model="newDevice.url"
                placeholder="Bark Push URL: https://day.app/your_key/"
                class="input-field"
              >
            </div>
          </div>

          <button 
            @click="addServer"
            :disabled="!canAddServer"
            class="btn-primary"
            :class="{ 'opacity-50 cursor-not-allowed': !canAddServer }"
          >
            添加设备
          </button>
        </div>
      </div>


      <!-- 使用提示 -->
      <div class="card">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">使用提示</h2>
        <ul class="space-y-2 text-gray-700 dark:text-gray-300">
          <li class="flex items-start">
            <span class="w-2 h-2 bg-bark-orange dark:bg-bark-orange-dark rounded-full mt-2 mr-3 flex-shrink-0"></span>
            如果您设置了多个地址，请使用右键菜单将消息推送到指定设备
          </li>
          <li class="flex items-start">
            <span class="w-2 h-2 bg-bark-orange dark:bg-bark-orange-dark rounded-full mt-2 mr-3 flex-shrink-0"></span>
            点击扩展图标可以快速推送剪贴板内容或当前页面 URL
          </li>
          <li class="flex items-start">
            <span class="w-2 h-2 bg-bark-orange dark:bg-bark-orange-dark rounded-full mt-2 mr-3 flex-shrink-0"></span>
            选中文本后右键可以直接推送选中的内容
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue'
  import { storage, defaultSettings } from '../utils/storage.js'

// 验证 URL 的函数
const validateURL = (str) => {
  const regex = /(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/
  return regex.test(str)
}
const settings = ref({
  server_urls: [],
  default_push_content: 'clipboard',
  auto_copy: 'no'
})
const statusMessage = ref('')
const statusClass = ref('')
const newDevice = ref({
  name: '',
  url: ''
})

// 测试相关的响应式数据
const testingStates = ref({}) // 记录每个设备的测试状态
const testResults = ref({}) // 记录每个设备的测试结果
const isTestingAll = ref(false) // 是否正在测试全部设备

// 计算属性

const canAddServer = computed(() => {
  return newDevice.value.name.trim() && newDevice.value.url.trim()
})

// 加载设置
const loadSettings = async () => {
  try {
    // 获取所有已保存的数据
    const savedSettings = await storage.get(null) // null 表示获取所有数据

    console.log('Loaded raw data:', savedSettings)
    console.log('savedSettings.server_urls:', savedSettings.server_urls)
    console.log('Is array:', Array.isArray(savedSettings.server_urls))
    console.log('Type:', typeof savedSettings.server_urls)

    // 确保 server_urls 是数组，并保持响应式
    let loadedServerUrls = defaultSettings.server_urls

    if (savedSettings.server_urls) {
      if (Array.isArray(savedSettings.server_urls)) {
        loadedServerUrls = savedSettings.server_urls
      } else {
        // 如果是对象，尝试转换为数组
        console.warn('server_urls is not an array, attempting to convert:', savedSettings.server_urls)
        const urlsArray = Object.values(savedSettings.server_urls)
        if (urlsArray.length > 0 && urlsArray[0].server_name && urlsArray[0].server_url) {
          loadedServerUrls = urlsArray
          console.log('Converted to array:', loadedServerUrls)
        }
      }
    }

    console.log('Final loadedServerUrls:', loadedServerUrls)

    // 清空现有数组并添加新数据，确保响应式
    settings.value.server_urls.splice(0, settings.value.server_urls.length, ...loadedServerUrls)

    console.log('Updated settings.value.server_urls:', settings.value.server_urls)
    console.log('Is array after update:', Array.isArray(settings.value.server_urls))
    settings.value.default_push_content = savedSettings.default_push_content ?? defaultSettings.default_push_content
    settings.value.auto_copy = savedSettings.auto_copy ?? defaultSettings.auto_copy
  } catch (error) {
    console.error('Load settings failed:', error)
    // 如果加载失败，重置为默认设置
    settings.value.server_urls = []
    settings.value.default_push_content = defaultSettings.default_push_content
    settings.value.auto_copy = defaultSettings.auto_copy
    showStatus('加载设置失败，使用默认设置', 'error')
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    // 确保数据结构正确
    const dataToSave = {
      server_urls: Array.isArray(settings.value.server_urls) ? [...settings.value.server_urls] : [],
      default_push_content: settings.value.default_push_content || 'clipboard',
      auto_copy: settings.value.auto_copy || 'no'
    }

    console.log('Saving data:', dataToSave)
    console.log('server_urls is array:', Array.isArray(dataToSave.server_urls))
    console.log('server_urls content:', dataToSave.server_urls)

    await storage.set(dataToSave)
    showStatus('设置已保存', 'success')

    // 通知 background script 更新菜单
    try {
      chrome.runtime.sendMessage({ greeting: "hello" })
    } catch (msgError) {
      console.warn('Failed to notify background script:', msgError)
    }
  } catch (error) {
    console.error('Save settings failed:', error)
    showStatus(`保存失败：${error.message || '未知错误'}`, 'error')
  }
}

// 添加服务器
const addServer = async () => {
  if (!canAddServer.value) return

  // 验证 URL
  if (!validateURL(newDevice.value.url)) {
    showStatus('请输入有效的 URL', 'error')
    return
  }

  const newServer = {
    server_name: newDevice.value.name.trim(),
    server_url: newDevice.value.url.trim()
  }

  // 确保 server_urls 是数组
  if (!Array.isArray(settings.value.server_urls)) {
    settings.value.server_urls = []
  }

  settings.value.server_urls.push(newServer)
  await saveSettings()

  // 清空表单
  newDevice.value.name = ''
  newDevice.value.url = ''

  showStatus('设备添加成功', 'success')
}

// 删除服务器
const deleteServer = async (index) => {
  if (confirm('确定要删除这个设备吗？')) {
    // 确保 server_urls 是数组
    if (!Array.isArray(settings.value.server_urls)) {
      settings.value.server_urls = []
      return
    }

    settings.value.server_urls.splice(index, 1)
    await saveSettings()
    showStatus('设备已删除', 'success')
  }
}



// 测试设备推送功能
const testDevice = async (index) => {
  const server = settings.value.server_urls[index]
  if (!server) return

  // 设置测试状态
  testingStates.value[index] = true
  testResults.value[index] = null

  try {
    const testMessage = `测试消息 - ${new Date().toLocaleTimeString()}`

    // 直接发送 HTTP 请求，避免使用 PushService 的 showError
    const serverUrl = server.server_url

    // 检查是否是 HTTP URL
    if (/^https?:\/\//i.test(serverUrl)) {
      // 构建推送 URL
      // const { origin, pathname, search } = new URL(serverUrl)
      // const params = search.slice(1)
      // const baseUrl = `${origin}${pathname}/`
      // const pushUrl = `${baseUrl}${encodeURIComponent(testMessage)}?automaticallyCopy=0${params ? '&' + params : ''}`
      const param = {
        title: 'Chrome Bark通知',
        body: testMessage,
        group: 'Chrome Bark通知'
      }
      // 发送 POST 请求
      const response = await fetch(serverUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(param)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.text();
      if (result === 'error') {
        throw new Error('服务器返回错误');
      }
    } else {
      throw new Error('仅支持 HTTP/HTTPS URL 的测试')
    }

    // 测试成功
    testResults.value[index] = {
      success: true,
      message: '测试成功！请检查设备是否收到通知'
    }

  } catch (error) {

    // 测试失败
    testResults.value[index] = {
      success: false,
      message: `测试失败：${error.message || '网络错误或设备配置有误'}`
    }
  } finally {
    // 清除测试状态
    testingStates.value[index] = false

    // 5秒后清除测试结果
    setTimeout(() => {
      if (testResults.value[index]) {
        testResults.value[index] = null
      }
    }, 5000)
  }
}

// 测试全部设备
const testAllDevices = async () => {
  if (!Array.isArray(settings.value.server_urls) || settings.value.server_urls.length === 0) {
    showStatus('没有可测试的设备', 'error')
    return
  }

  isTestingAll.value = true

  try {
    // 并行测试所有设备
    const testPromises = settings.value.server_urls.map((_, index) => testDevice(index))
    await Promise.allSettled(testPromises)

    // 统计测试结果
    const successCount = Object.values(testResults.value).filter(result => result?.success).length
    const totalCount = settings.value.server_urls.length

    if (successCount === totalCount) {
      showStatus(`全部 ${totalCount} 个设备测试成功`, 'success')
    } else {
      showStatus(`${successCount}/${totalCount} 个设备测试成功，请检查失败的设备配置`, 'error')
    }

  } catch (error) {
    console.error('Test all devices failed:', error)
    showStatus('批量测试失败', 'error')
  } finally {
    isTestingAll.value = false
  }
}



// 显示状态消息
const showStatus = (message, type) => {
  statusMessage.value = message
  statusClass.value = type === 'success'
    ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700'
    : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-700'

  setTimeout(() => {
    statusMessage.value = ''
  }, 3000)
}

// 修复存储数据格式（临时函数）
const fixStorageFormat = async () => {
  try {
    const rawData = await storage.get(null)
    if (rawData.server_urls && !Array.isArray(rawData.server_urls)) {
      console.log('Fixing corrupted storage format...')
      const urlsArray = Object.values(rawData.server_urls)
      const fixedData = {
        ...rawData,
        server_urls: urlsArray
      }
      await storage.set(fixedData)
      console.log('Storage format fixed!')
    }
  } catch (error) {
    console.error('Failed to fix storage format:', error)
  }
}

// 页面加载时执行
onMounted(async () => {
  await fixStorageFormat()
  await loadSettings()
})
</script>
