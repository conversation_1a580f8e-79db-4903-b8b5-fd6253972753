<template>
  <div class="p-4 bg-white dark:bg-gray-800">
    <div class="flex items-center mb-4">
      <img src="/assets/bark_48.png" alt="Bark" class="w-8 h-8 mr-2">
      <h1 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Bark 推送</h1>
    </div>

    <div class="space-y-3">
      <!-- 快速推送按钮 -->
      <button 
        @click="pushClipboard"
        :disabled="loading"
        class="btn-primary w-full"
      >
        <span v-if="loading">推送中...</span>
        <span v-else>推送剪贴板</span>
      </button>

      <button 
        @click="pushCurrentUrl"
        :disabled="loading"
        class="btn-secondary w-full"
      >
        推送当前页面
      </button>

      <!-- 服务器状态 -->
      <div v-if="servers.length > 0" class="text-sm text-gray-600 dark:text-gray-400">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-2"></div>
          已配置 {{ servers.length }} 个设备
        </div>
      </div>

      <div v-else class="text-sm text-red-600 dark:text-red-400">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-red-500 dark:bg-red-400 rounded-full mr-2"></div>
          未配置设备
        </div>
      </div>

      <!-- 设置按钮 -->
      <button 
        @click="openOptions"
        class="btn-secondary w-full text-sm"
      >
        打开设置
      </button>
    </div>

    <!-- 状态消息 -->
    <div v-if="message" class="mt-3 p-2 rounded text-sm" :class="messageClass">
      {{ message }}
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { storage, defaultSettings } from '../utils/storage.js'

export default {
  name: 'PopupApp',
  setup() {
    const loading = ref(false)
    const message = ref('')
    const messageClass = ref('')
    const servers = ref([])

    // 加载设置
    const loadSettings = async () => {
      const settings = await storage.get(defaultSettings)
      servers.value = settings.server_urls || []
    }

    // 推送剪贴板内容
    const pushClipboard = async () => {
      if (servers.value.length === 0) {
        showMessage('请先在设置中配置服务器', 'error')
        return
      }

      loading.value = true
      try {
        // 发送消息给 background script
        await chrome.runtime.sendMessage({
          action: 'pushClipboard'
        })
        showMessage('推送成功', 'success')
      } catch (error) {
        console.error('Push failed:', error)
        showMessage('推送失败', 'error')
      } finally {
        loading.value = false
      }
    }

    // 推送当前页面 URL
    const pushCurrentUrl = async () => {
      if (servers.value.length === 0) {
        showMessage('请先在设置中配置服务器', 'error')
        return
      }

      loading.value = true
      try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
        if (tabs.length > 0) {
          await chrome.runtime.sendMessage({
            action: 'pushUrl',
            url: tabs[0].url
          })
          showMessage('推送成功', 'success')
        }
      } catch (error) {
        console.error('Push failed:', error)
        showMessage('推送失败', 'error')
      } finally {
        loading.value = false
      }
    }

    // 打开设置页面
    const openOptions = () => {
      chrome.tabs.create({ url: 'options.html' })
      window.close()
    }

    // 显示消息
    const showMessage = (msg, type) => {
      message.value = msg
      messageClass.value = type === 'success'
        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700'
        : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-700'

      setTimeout(() => {
        message.value = ''
      }, 3000)
    }

    onMounted(() => {
      loadSettings()
    })

    return {
      loading,
      message,
      messageClass,
      servers,
      pushClipboard,
      pushCurrentUrl,
      openOptions
    }
  }
}
</script>
